@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600&display=swap');

*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    text-decoration: none;
    border: none;
    outline: none;
    font-family: 'Poppins', sans-serif;
}

html{
    font-size: 62.5%;
}

body{
    width: 100%;
    height: 100vh;
    overflow-x: hidden;
    background-color: black;
    color: white;
}

/* Enhanced Navbar Styles */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(230, 139, 4, 0.2);
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background: rgba(0, 0, 0, 0.98);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Logo Styles */
.logo {
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.logo-text {
    font-size: 2.5rem;
    color: #e68b04;
    font-weight: 800;
    background: linear-gradient(45deg, #e68b04, #ffa726);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    transition: all 0.3s ease;
}

.logo:hover .logo-text {
    transform: scale(1.05);
    filter: brightness(1.2);
}

/* Navigation Menu */
.nav-menu {
    display: flex;
}

.nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.5rem;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    color: #fff;
    text-decoration: none;
    font-size: 2.1rem;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(230, 139, 4, 0.1), transparent);
    transition: left 0.5s ease;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link i {
    font-size: 1rem;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: #e68b04;
    background: rgba(230, 139, 4, 0.1);
    transform: translateY(-2px);
}

.nav-link:hover i,
.nav-link.active i {
    transform: scale(1.1);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background: #e68b04;
    border-radius: 2px;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-around;
    width: 30px;
    height: 30px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 1001;
}

.hamburger-line {
    width: 100%;
    height: 3px;
    background: #e68b04;
    border-radius: 2px;
    transition: all 0.3s ease;
    transform-origin: center;
}

.mobile-menu-toggle:hover .hamburger-line {
    background: #ffa726;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(7px, 7px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -7px);
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    .nav-container {
        padding: 1rem;
    }

    .logo-text {
        font-size: 2rem;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        top: 0;
        right: -100%;
        width: 280px;
        height: 100vh;
        background: rgba(10, 10, 10, 0.98);
        -webkit-backdrop-filter: blur(20px);
        backdrop-filter: blur(20px);
        border-left: 1px solid rgba(230, 139, 4, 0.3);
        transition: right 0.3s ease;
        padding-top: 80px;
        z-index: 999;
    }

    .nav-menu.active {
        right: 0;
    }

    .nav-list {
        flex-direction: column;
        gap: 0;
        padding: 2rem 1rem;
    }

    .nav-link {
        padding: 1.2rem 1.5rem;
        font-size: 1.2rem;
        border-radius: 0;
        border-bottom: 1px solid rgba(230, 139, 4, 0.1);
    }

    .nav-link:hover,
    .nav-link.active {
        background: rgba(230, 139, 4, 0.15);
        transform: translateX(10px);
        border-left: 3px solid #e68b04;
    }

    .nav-link.active::after {
        display: none;
    }

    .nav-link i {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0.8rem;
    }

    .logo-text {
        font-size: 1.8rem;
    }

    .nav-menu {
        width: 100%;
        right: -100%;
    }

    .nav-list {
        padding: 1.5rem;
    }

    .nav-link {
        padding: 1rem 1.2rem;
        font-size: 1.1rem;
    }
}

section{
    min-height: 100vh;
    padding: 5rem 9% 5rem;
}

.home{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8rem;
    background-color: black;
}

.home .home-content h1{
    font-size: 4.5rem;
    font-weight: 700;
    line-height: 1.3;
}

span{
    color: #e68b04;
}

.home-content h3{
    font-size: 4rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.home-content p{
    font-size: 1.6rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* Home Content Sections */
.intro-section {
    margin-bottom: 4rem;
}

.intro-description {
    max-width: 600px;
    color: #ccc;
    margin-top: 2rem;
}

.about-section {
    margin-bottom: 4rem;
}

.about-section h2 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    line-height: 1.3;
}

.about-content {
    max-width: 700px;
}

.about-content p {
    color: #ccc;
    margin-bottom: 2rem;
    text-align: justify;
}

.contact-section {
    margin-top: 3rem;
}

/* Contact Page Styles */
.contact {
    min-height: 100vh;
    padding: 8rem 2rem 4rem;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    display: flex;
    align-items: center;
}

.contact-container {
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.contact-header {
    text-align: center;
    margin-bottom: 4rem;
}

.contact-header h2 {
    font-size: 3.5rem;
    font-weight: 700;
    color: #fff;
    margin-bottom: 1rem;
}

.contact-header h2 span {
    color: #e68b04;
}

.contact-header p {
    font-size: 1.2rem;
    color: #ccc;
    max-width: 600px;
    margin: 0 auto;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: 4rem;
    align-items: start;
}

/* Contact Info Styles */
.contact-info {
    background: rgba(255, 255, 255, 0.05);
    padding: 3rem;
    border-radius: 20px;
    border: 1px solid rgba(230, 139, 4, 0.2);
    backdrop-filter: blur(10px);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2.5rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: rgba(230, 139, 4, 0.1);
    transform: translateX(10px);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #e68b04, #ffa726);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #000;
    flex-shrink: 0;
}

.contact-details h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: #ccc;
    font-size: 1rem;
}

.contact-social {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(230, 139, 4, 0.2);
}

.contact-social h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: 1rem;
}

.contact-social .social-links {
    display: flex;
    gap: 1rem;
}

.contact-social .social-links a {
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ccc;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.contact-social .social-links a:hover {
    btnackground: #e68b04;
    color: #000;
    transform: translateY(-3px);
}

/* Contact Form Styles */
.contact-form-container {
    background: rgba(255, 255, 255, 0.05);
    padding: 3rem;
    border-radius: 20px;
    border: 1px solid rgba(230, 139, 4, 0.2);
    backdrop-filter: blur(10px);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-size: 1rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(230, 139, 4, 0.3);
    border-radius: 8px;
    color: #fff;
    font-size: 1rem;
    transition: all 0.3s ease;
    resize: vertical;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #e68b04;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(230, 139, 4, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #999;
}

.form-group select option {
    background: #1a1a1a;
    color: #fff;
}

.btn-text {
  color:white;
}

.submit-btn {
    padding: 1.2rem 2rem;
    background: linear-gradient(135deg, #e68b04, #ffa726);
    border: none;
    border-radius: 8px;
    color: #000;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(230, 139, 4, 0.3);
}

.submit-btn:active {
    transform: translateY(0);
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-loading {
    display: none;
    color:white;
    align-items: center; 
    gap: 0.5rem;      
}  

.form-status {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    font-weight: 600;
    text-align: center;
    display: none;
}

.form-status.success {
    background: rgba(76, 175, 80, 0.2);
    border: 1px solid #4caf50;
    color: #4caf50;
}

.form-status.error {
    background: rgba(244, 67, 54, 0.2);
    border: 1px solid #f44336;
    color: #f44336;
}

/* Contact Responsive Styles */
@media (max-width: 768px) {
    .contact {
        padding: 6rem 1rem 2rem;
    }

    .contact-header h2 {
        font-size: 2.5rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-info,
    .contact-form-container {
        padding: 2rem;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .contact-item:hover {
        transform: translateY(-5px);
    }

    .contact-social .social-links {
        justify-content: center;
    }
}

.home-img img {
    position: relative;
    width: 32vw;
    cursor: pointer;
    border-radius: 12px;
    border: 3px solid #e68b04;
    transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.home-img img:hover {
    box-shadow: 0 0 20px #e68b04;
    transform: scale(1.03);
}


.social-icons a{
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 4rem;
    height: 4rem;
    background-color: transparent;
    border: 0.2rem solid #e68b04;
    font-size: 2rem;
    border-radius: 50%;
    margin: 3rem 1.5rem 3rem 0;
    transition: 0.3s ease;
    color: #e68b04;
}

.social-icons a:hover{
    color: black;
    transform: scale(1.3) translateY(-5px);
    background-color: #e68b04;
    box-shadow: 0  0 25px #e68b04;
}

.btn{
    display: inline-block;
    padding: 1rem 2.8rem;
    background-color: black;
    border-radius: 4rem;
    font-size: 1.6rem;
    color: #e68b04;
    letter-spacing: 0.3rem;
    font-weight: 600;
    border: 2px solid #e68b04;
    transition: 0.3s ease;
    cursor: pointer;
}

.btn:hover{
    transform: scale3d(1.03);
    background-color: #e68b04;
    color: black;
    box-shadow: 0 0 25px #e68b04;
}

.typing-text{
    font-size: 34px;
    font-weight: 600;
    min-width: 280px;
}

.typing-text span{
    position: relative;
}

.typing-text span::before{
    content: "software Developer";
    color: #e68b04;
    animation: words 20s infinite;
}

.typing-text span::after{
    content: "";
    background-color: black;
    position: absolute;
    width: calc(100% + 8px);
    height: 100%;
    border-left: 3px solid black;
    right: -8;
    animation: cursor 0.6s infinite;
}

@keyframes cursor{
    to{
        border-left: 3px solid #e68b04;
    }
}

@keyframes words{
    0%, 20%{
        content: "Web Developer";
    }
    21%, 40%{
        content: "Project Manager";
    }
    41%, 60%{
        content: "Web Designer";
    }
    61%, 80%{
        content: "MERN Stack Developer";
    }
    81%, 100%{
        content: "MEAN Stack Developer";
    }
}

@media (max-width: 1000px){
    .home{
        gap: 4rem;
    }
}

@media(max-width:995px){
    .home{
        flex-direction: column;
        margin: 5rem 4rem;
    }

    .home .home-content h3{
        font-size: 2.5rem;
    }

    .home-content h1{
        font-size: 5rem;
    }

    .about-section h2 {
        font-size: 3rem;
    }

    .intro-section {
        margin-bottom: 3rem;
    }

    .about-section {
        margin-bottom: 3rem;
    }

    .about-content p {
        text-align: left;
    }

    .home-img img{
        width: 70vw;
        margin-top: 4rem;
    }
}

/* Footer Styles */
.site-footer {
  background: linear-gradient(135deg, #0a0a0a 0%, #111 100%);
  padding: 5rem 9% 2rem;
  font-size: 1.3rem;
  line-height: 1.6;
  border-top: 2px solid rgba(230,139,4,0.3);
  position: relative;
}

.site-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #e68b04, transparent);
}

.footer-inner {
  max-width: 1200px;
  margin: 0 auto;
}

.footer-main {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 4rem;
  margin-bottom: 3rem;
  padding-bottom: 3rem;
  border-bottom: 1px solid rgba(230,139,4,0.2);
}

/* Footer Brand */
.footer-brand {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-logo {
  font-size: 2.5rem;
  color: #e68b04;
  font-weight: 800;
  margin: 0;
  margin-bottom: 0.5rem;
}

.footer-name {
  font-weight: 600;
  font-size: 1.4rem;
  color: #fff;
  margin: 0;
}

.footer-status {
  color: #bbb;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin: 0;
}

.status-indicator {
  width: 8px;
  height: 8px;
  background: #4ade80;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Footer Links */
.footer-links {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
}

.footer-contact h4,
.footer-social h4 {
  color: #e68b04;
  font-size: 1.6rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  margin-top: 0;
}

/* Contact Info */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.contact-email,
.contact-location {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #ccc;
  transition: all 0.3s ease;
}

.contact-email {
  text-decoration: none;
  padding: 0.8rem 0;
  border-radius: 8px;
}

.contact-email:hover {
  color: #e68b04;
  transform: translateX(5px);
}

.contact-email i,
.contact-location i {
  width: 20px;
  color: #e68b04;
  font-size: 1.1rem;
}

/* Social Links */
.social-links {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.social-links a {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #ccc;
  text-decoration: none;
  padding: 0.8rem 1rem;
  border-radius: 8px;
  border: 1px solid rgba(230,139,4,0.2);
  transition: all 0.3s ease;
  background: rgba(230,139,4,0.05);
}

.social-links a:hover {
  background: rgba(230,139,4,0.1);
  border-color: #e68b04;
  color: #e68b04;
  transform: translateX(5px);
}

.social-links a i {
  width: 20px;
  font-size: 1.2rem;
}

/* Footer Bottom */
.footer-bottom {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
  text-align: center;
}

.footer-tech {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.tech-label {
  color: #e68b04;
  font-weight: 600;
  font-size: 1.2rem;
}

.tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  justify-content: center;
}

.tech-item {
  background: rgba(230,139,4,0.1);
  color: #e68b04;
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 1rem;
  font-weight: 500;
  border: 1px solid rgba(230,139,4,0.3);
  transition: all 0.3s ease;
}

.tech-item:hover {
  background: rgba(230,139,4,0.2);
  transform: translateY(-2px);
}

.footer-note {
  color: #888;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.footer-note i {
  color: #e68b04;
  animation: heartbeat 2s infinite;
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer-main {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .footer-links {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-bottom {
    text-align: center;
  }

  .tech-stack {
    justify-content: center;
  }

  .social-links a {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .site-footer {
    padding: 3rem 5% 2rem;
  }

  .footer-logo {
    font-size: 2rem;
  }

  .footer-main {
    gap: 2rem;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
  }

  .footer-links {
    gap: 1.5rem;
  }

  .tech-stack {
    gap: 0.5rem;
  }

  .tech-item {
    padding: 0.3rem 0.8rem;
    font-size: 0.9rem;
  }
}

/* Education Page Styles */
.education-page {
  padding-top: 80px;
  min-height: 100vh;
}

.education-hero {
  background: linear-gradient(135deg, #000 0%, #111 50%, #000 100%);
  padding: 7rem 9% 6rem;
  text-align: center;
  position: relative;
}

.education-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e68b04" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-content h1 {
  font-size: 5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: #fff;
}

.hero-subtitle {
  font-size: 1.8rem;
  color: #ccc;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.container {
  max-width: 80%;
  margin: 0 auto;
  padding: 0 2rem;
}

.section-title {
  font-size: 3.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 4rem;
  color: #fff;
}

/* Timeline Styles */
.education-timeline {
  padding: 8rem 0;
  background: #0a0a0a;
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #e68b04, #ffa726);
}

.timeline-item {
  position: relative;
  margin-bottom: 4rem;
  padding-left: 80px;
}

.timeline-marker {
  position: absolute;
  left: 15px;
  top: 0;
  width: 30px;
  height: 30px;
  background: #e68b04;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000;
  font-size: 1.2rem;
  font-weight: bold;
  box-shadow: 0 0 20px rgba(230, 139, 4, 0.5);
}

.timeline-content {
  background: rgba(230, 139, 4, 0.05);
  border: 1px solid rgba(230, 139, 4, 0.2);
  border-radius: 12px;
  padding: 2.5rem;
  transition: all 0.3s ease;
}

.timeline-content:hover {
  background: rgba(230, 139, 4, 0.1);
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(230, 139, 4, 0.2);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.timeline-header h3 {
  font-size: 2rem;
  font-weight: 600;
  color: #e68b04;
  margin: 0;
}

.timeline-date {
  background: rgba(230, 139, 4, 0.2);
  color: #e68b04;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
  font-size: 1rem;
}

.institution {
  font-size: 1.4rem;
  font-weight: 600;
  color: #fff;
  margin: 0.5rem 0;
}

.location {
  color: #ccc;
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.education-details,
.achievements {
  margin-top: 2rem;
}

.education-details h5,
.achievements h5 {
  color: #e68b04;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding: 12px;
}

.coursework-list,
.achievement-list {
  list-style: none;
  padding: 12px;
  margin: 0;
}

.coursework-list li,
.achievement-list li {
  color: #ccc;
  padding: 0.5rem 0;
  padding-left: 2rem;
  position: relative;
  line-height: 1.6;
}

.coursework-list li::before {
  content: '▸';
  color: #e68b04;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.achievement-list li::before {
  content: '★';
  color: #e68b04;
  position: absolute;
  left: 0;
}

/* Certifications Section */
.certifications {
  padding: 8rem 0;
  background: #111;
}

.cert-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.cert-card {
  background: rgba(230, 139, 4, 0.05);
  border: 1px solid rgba(230, 139, 4, 0.2);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.cert-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(230, 139, 4, 0.1), transparent);
  transition: left 0.5s ease;
}

.cert-card:hover::before {
  left: 100%;
}

.cert-card:hover {
  background: rgba(230, 139, 4, 0.1);
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(230, 139, 4, 0.2);
}

.cert-icon {
  font-size: 3rem;
  color: #e68b04;
  margin-bottom: 1.5rem;
}

.cert-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 1rem;
}

.cert-card p {
  color: #ccc;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.cert-status {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.cert-status:contains("Completed") {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.cert-status:contains("In Progress") {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.cert-status:contains("Ongoing") {
  background: rgba(33, 150, 243, 0.2);
  color: #2196f3;
  border: 1px solid rgba(33, 150, 243, 0.3);
}

/* Skills Acquired Section */
.skills-acquired {
  padding: 8rem 0;
  background: #0a0a0a;
}

.skills-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 3rem;
  margin-top: 3rem;
}

.skill-category {
  background: rgba(230, 139, 4, 0.05);
  border: 1px solid rgba(230, 139, 4, 0.2);
  border-radius: 12px;
  padding: 2.5rem;
  transition: all 0.3s ease;
}

.skill-category:hover {
  background: rgba(230, 139, 4, 0.1);
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(230, 139, 4, 0.2);
}

.skill-category h3 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #e68b04;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.skill-category h3 i {
  font-size: 1.5rem;
}

.skill-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.skill-tag {
  background: rgba(230, 139, 4, 0.1);
  color: #e68b04;
  padding: 0.6rem 1.2rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  border: 1px solid rgba(230, 139, 4, 0.3);
  transition: all 0.3s ease;
  cursor: default;
}

.skill-tag:hover {
  background: rgba(230, 139, 4, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(230, 139, 4, 0.3);
}

/* Education Page Responsive Styles */
@media (max-width: 768px) {
  .education-hero {
    padding: 6rem 5% 4rem;
  }

  .hero-content h1 {
    font-size: 3.5rem;
  }

  .hero-subtitle {
    font-size: 1.4rem;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .education-timeline,
  .certifications,
  .skills-acquired {
    padding: 5rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .timeline::before {
    left: 20px;
  }

  .timeline-item {
    padding-left: 60px;
  }

  .timeline-marker {
    left: 5px;
    width: 25px;
    height: 25px;
    font-size: 1rem;
  }

  .timeline-content {
    padding: 2rem;
  }

  .timeline-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .timeline-header h3 {
    font-size: 1.6rem;
  }

  .cert-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .skills-categories {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .skill-category {
    padding: 2rem;
  }

  .skill-category h3 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .education-hero {
    padding: 4rem 3% 3rem;
  }

  .hero-content h1 {
    font-size: 2.8rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .timeline-content {
    padding: 1.5rem;
  }

  .timeline-header h3 {
    font-size: 1.4rem;
  }

  .institution {
    font-size: 1.2rem;
  }

  .cert-card {
    padding: 1.5rem;
  }

  .skill-category {
    padding: 1.5rem;
  }

  .skill-tags {
    gap: 0.8rem;
  }

  .skill-tag {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

/* Education Gallery Styles */
.education-gallery {
  padding: 8rem 0;
}

.gallery-subtitle {
  text-align: center;
  font-size: 1.6rem;
  color: #ccc;
  margin-bottom: 4rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.gallery-item {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.4s ease;
  background: rgba(230, 139, 4, 0.05);
  border: 1px solid rgba(230, 139, 4, 0.2);
}

.gallery-item:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 20px 40px rgba(230, 139, 4, 0.3);
}

.gallery-image {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.gallery-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.gallery-item:hover .gallery-image img {
  transform: scale(1.1);
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.8) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 1.5rem;
  opacity: 0;
  transition: all 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-info {
  margin-top: auto;
}

.gallery-info h4 {
  color: #fff;
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.gallery-info p {
  color: #ccc;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.gallery-date {
  color: #e68b04;
  font-size: 0.9rem;
  font-weight: 500;
  background: rgba(230, 139, 4, 0.2);
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  display: inline-block;
}

.gallery-expand {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(230, 139, 4, 0.9);
  border: none;
  color: #000;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1rem;
}

.gallery-expand:hover {
  background: #e68b04;
  transform: scale(1.1);
}

.gallery-filters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
  margin-top: 3rem;
}

.filter-btn {
  background: rgba(230, 139, 4, 0.1);
  border: 1px solid rgba(230, 139, 4, 0.3);
  color: #e68b04;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 10;
  pointer-events: auto;
  -webkit-user-select: none;
}

.filter-btn:hover,
.filter-btn.active {
  background: #e68b04;
  color: #000;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(230, 139, 4, 0.4);
}

.filter-btn:focus {
  outline: 2px solid #e68b04;
  outline-offset: 2px;
}

.gallery-item.hidden {
  display: none;
}

/* Gallery Modal */
.gallery-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.gallery-modal.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  background: #111;
  border-radius: 15px;
  overflow: hidden;
  border: 2px solid #e68b04;
}

.modal-image {
  width: 100%;
  height: auto;
  display: block;
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(230, 139, 4, 0.9);
  border: none;
  color: #000;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: #e68b04;
  transform: scale(1.1);
}

/* Gallery Responsive Styles */
@media (max-width: 768px) {
  .education-gallery {
    padding: 5rem 0;
  }

  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .gallery-image {
    height: 200px;
  }

  .gallery-filters {
    gap: 0.8rem;
  }

  .filter-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }

  .modal-content {
    max-width: 95%;
    max-height: 85%;
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .gallery-image {
    height: 180px;
  }

  .gallery-overlay {
    padding: 1rem;
  }

  .gallery-info h4 {
    font-size: 1.2rem;
  }

  .gallery-filters {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }

  .filter-btn {
    width: 200px;
    text-align: center;
  }
}

/* Experience Page Styles */
.experience-page {
  padding-top: 80px;
  min-height: 100vh;
}

.experience-hero {
  background: linear-gradient(135deg, #000 0%, #111 50%, #000 100%);
  padding: 8rem 9% 6rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.experience-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(230, 139, 4, 0.1) 0%, transparent 70%);
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-content h1 {
  font-size: 5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: #fff;
}

.hero-subtitle {
  font-size: 1.8rem;
  color: #ccc;
  max-width: 700px;
  margin: 0 auto 4rem;
  line-height: 1.6;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 4rem;
  margin-top: 4rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 3rem;
  font-weight: 700;
  color: #e68b04;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1.2rem;
  color: #ccc;
  font-weight: 500;
}

/* Work Experience Section */
.work-experience {
  padding: 8rem 0;
  background: #0a0a0a;
}

.experience-timeline {
  position: relative;
  max-width: 900px;
  margin: 0 auto;
}

.experience-timeline::before {
  content: '';
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(to bottom, #e68b04, #ffa726, #e68b04);
}

.experience-item {
  position: relative;
  margin-bottom: 5rem;
  padding-left: 90px;
}

.experience-marker {
  position: absolute;
  left: 15px;
  top: 0;
  width: 35px;
  height: 35px;
  background: linear-gradient(45deg, #e68b04, #ffa726);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000;
  font-size: 1.3rem;
  font-weight: bold;
  box-shadow: 0 0 25px rgba(230, 139, 4, 0.6);
  border: 3px solid #000;
}

.experience-content {
  background: rgba(230, 139, 4, 0.05);
  border: 1px solid rgba(230, 139, 4, 0.2);
  border-radius: 15px;
  padding: 3rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.experience-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(230, 139, 4, 0.1), transparent);
  transition: left 0.6s ease;
}

.experience-content:hover::before {
  left: 100%;
}

.experience-content:hover {
  background: rgba(230, 139, 4, 0.1);
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(230, 139, 4, 0.2);
}

.experience-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.job-info h3 {
  font-size: 2.2rem;
  font-weight: 600;
  color: #e68b04;
  margin: 0 0 0.5rem 0;
}

.company {
  font-size: 1.6rem;
  font-weight: 600;
  color: #fff;
  margin: 0 0 0.5rem 0;
}

.location {
  color: #ccc;
  font-size: 1.1rem;
  margin: 0;
}

.experience-date {
  background: rgba(230, 139, 4, 0.2);
  color: #e68b04;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 1rem;
  white-space: nowrap;
}

.job-description {
  color: #ccc;
  font-size: 1.2rem;
  line-height: 1.7;
  margin-bottom: 2rem;
}

.experience-details h5 {
  color: #e68b04;
  font-size: 1.4rem;
  font-weight: 600;
  margin: 2rem 0 1rem 0;
}

.responsibility-list {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
}

.responsibility-list li {
  color: #ccc;
  padding: 0.8rem 0;
  padding-left: 2.5rem;
  position: relative;
  line-height: 1.6;
  font-size: 1.1rem;
}

.responsibility-list li::before {
  content: '▶';
  color: #e68b04;
  font-weight: bold;
  position: absolute;
  left: 0;
  top: 0.8rem;
}

.tech-stack-used {
  margin-top: 2rem;
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin-top: 1rem;
}

/* Project Experience Section */
.project-experience {
  padding: 8rem 0;
  background: #111;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 3rem;
  margin-top: 4rem;
}

.project-card {
  background: rgba(230, 139, 4, 0.05);
  border: 1px solid rgba(230, 139, 4, 0.2);
  border-radius: 15px;
  padding: 3rem;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(230, 139, 4, 0.1), transparent);
  transition: left 0.6s ease;
}

.project-card:hover::before {
  left: 100%;
}

.project-card:hover {
  background: rgba(230, 139, 4, 0.1);
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 20px 50px rgba(230, 139, 4, 0.3);
}

.project-icon {
  font-size: 3.5rem;
  color: #e68b04;
  margin-bottom: 2rem;
  text-align: center;
}

.project-card h3 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 1.5rem;
  text-align: center;
}

.project-description {
  color: #ccc;
  line-height: 1.7;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.project-features h5 {
  color: #e68b04;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.project-features ul {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
}

.project-features li {
  color: #ccc;
  padding: 0.5rem 0;
  padding-left: 2rem;
  position: relative;
  line-height: 1.6;
}

.project-features li::before {
  content: '✓';
  color: #e68b04;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin-top: 2rem;
}

/* Achievements Section */
.achievements {
  padding: 8rem 0;
  background: rgba(230, 139, 4, 0.05);
  border: 1px solid rgba(230, 139, 4, 0.2);
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2.5rem;
  margin-top: 4rem;
}

.achievement-card {
  background: rgba(230, 139, 4, 0.05);
  border: 1px solid rgba(230, 139, 4, 0.2);
  border-radius: 15px;
  padding: 2.5rem;
  text-align: center;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.achievement-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(230, 139, 4, 0.1), transparent);
  transition: left 0.6s ease;
}

.achievement-card:hover::before {
  left: 100%;
}

.achievement-card:hover {
  background: rgba(230, 139, 4, 0.1);
  transform: translateY(-8px) rotate(2deg);
  box-shadow: 0 15px 40px rgba(230, 139, 4, 0.3);
}

.achievement-icon {
  font-size: 3rem;
  color: #e68b04;
  margin-bottom: 1.5rem;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.achievement-card h3 {
  font-size: 1.6rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 1rem;
}

.achievement-card p {
  color: #ccc;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

.achievement-year {
  display: inline-block;
  background: rgba(230, 139, 4, 0.2);
  color: #e68b04;
  padding: 0.5rem 1.2rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

/* Experience Page Responsive Styles */
@media (max-width: 768px) {
  .experience-hero {
    padding: 6rem 5% 4rem;
  }

  .hero-content h1 {
    font-size: 3.5rem;
  }

  .hero-subtitle {
    font-size: 1.4rem;
    margin-bottom: 3rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 2rem;
    align-items: center;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .work-experience,
  .project-experience,
  .achievements {
    padding: 5rem 0;
  }

  .experience-timeline::before {
    left: 20px;
  }

  .experience-item {
    padding-left: 70px;
  }

  .experience-marker {
    left: 5px;
    width: 30px;
    height: 30px;
    font-size: 1.1rem;
  }

  .experience-content {
    padding: 2rem;
  }

  .experience-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .job-info h3 {
    font-size: 1.8rem;
  }

  .company {
    font-size: 1.4rem;
  }

  .experience-date {
    align-self: flex-start;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .project-card {
    padding: 2rem;
  }

  .achievements-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
  }

  .achievement-card {
    padding: 2rem;
  }
}

@media (max-width: 480px) {
  .experience-hero {
    padding: 4rem 3% 3rem;
  }

  .hero-content h1 {
    font-size: 2.8rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .hero-stats {
    gap: 1.5rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .stat-label {
    font-size: 1rem;
  }

  .experience-content {
    padding: 1.5rem;
  }

  .job-info h3 {
    font-size: 1.6rem;
  }

  .company {
    font-size: 1.2rem;
  }

  .job-description {
    font-size: 1.1rem;
  }

  .responsibility-list li {
    font-size: 1rem;
  }

  .project-card {
    padding: 1.5rem;
  }

  .project-icon {
    font-size: 3rem;
  }

  .project-card h3 {
    font-size: 1.6rem;
  }

  .achievement-card {
    padding: 1.5rem;
  }

  .achievement-icon {
    font-size: 2.5rem;
  }

  .achievement-card h3 {
    font-size: 1.4rem;
  }

  .tech-tags {
    gap: 0.6rem;
  }

  .tech-tag {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }
}
